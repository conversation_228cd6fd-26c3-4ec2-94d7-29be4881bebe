"""
PlanAgent配置模块

提供PlanAgent的专门配置管理
"""
from typing import Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class ExecutionStrategy(str, Enum):
    """执行策略枚举"""
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    ADAPTIVE = "adaptive"  # 根据步骤数量自动选择


class PlanAgentConfig(BaseModel):
    """PlanAgent配置"""
    
    # 规划配置
    max_replanning_iterations: int = Field(
        default=3,
        ge=1,
        le=10,
        description="最大重规划次数"
    )
    
    # 执行配置
    execution_strategy: ExecutionStrategy = Field(
        default=ExecutionStrategy.ADAPTIVE,
        description="工具执行策略"
    )
    
    parallel_threshold: int = Field(
        default=2,
        ge=1,
        description="并行执行的最小步骤数阈值"
    )
    
    tool_execution_timeout: int = Field(
        default=30,
        ge=5,
        le=300,
        description="单个工具执行超时时间(秒)"
    )
    
    max_retries: int = Field(
        default=3,
        ge=1,
        le=10,
        description="工具执行最大重试次数"
    )
    
    # 质量评估阈值
    min_execution_quality: float = Field(
        default=0.6,
        ge=0.0,
        le=1.0,
        description="最低执行质量阈值"
    )
    
    min_task_completeness: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="最低任务完整度阈值"
    )
    
    # 人工审核配置
    enable_human_review: bool = Field(
        default=True,
        description="是否启用人工审核"
    )
    
    auto_approve_simple_plans: bool = Field(
        default=True,
        description="是否自动批准简单计划"
    )
    
    simple_plan_threshold: int = Field(
        default=2,
        ge=1,
        description="简单计划的步骤数阈值"
    )
    
    # 调试配置
    enable_detailed_logging: bool = Field(
        default=True,
        description="是否启用详细日志"
    )
    
    log_tool_results: bool = Field(
        default=True,
        description="是否记录工具执行结果"
    )


# 默认配置实例
DEFAULT_PLAN_AGENT_CONFIG = PlanAgentConfig()


def get_execution_strategy(config: PlanAgentConfig, step_count: int) -> ExecutionStrategy:
    """根据配置和步骤数量确定执行策略"""
    if config.execution_strategy == ExecutionStrategy.ADAPTIVE:
        return ExecutionStrategy.PARALLEL if step_count >= config.parallel_threshold else ExecutionStrategy.SEQUENTIAL
    return config.execution_strategy


def should_enable_human_review(config: PlanAgentConfig, plan) -> bool:
    """判断是否需要启用人工审核"""
    if not config.enable_human_review:
        return False
    
    if config.auto_approve_simple_plans and plan:
        return len(plan.steps) > config.simple_plan_threshold
    
    return True
