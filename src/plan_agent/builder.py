"""
Plan Agent 构建器
"""
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from .types import PlanAgentState
from .nodes.planning import create_planner_node
from .nodes.human_feedback import create_human_review_node
from .nodes.execution import create_executor_node
from .nodes.response_generator import create_quality_response_node


async def create_plan_agent_graph(llm, mcp_client, checkpointer=None, available_tools=None):
    """创建优化的Plan Agent图"""

    # 获取并绑定工具到LLM
    if available_tools is None:
        available_tools = await mcp_client.get_tools()

    # 绑定工具到LLM
    tool_bound_llm = llm.bind_tools(available_tools, tool_choice="none")

    builder = StateGraph(PlanAgentState)

    # 创建节点实例
    planner_node = create_planner_node(llm, mcp_client, available_tools)
    human_review_node = create_human_review_node()
    executor_node = create_executor_node(mcp_client, available_tools)
    quality_response_node = create_quality_response_node(tool_bound_llm)

    # 添加节点
    builder.add_node("planner", planner_node)
    builder.add_node("human_review", human_review_node)
    builder.add_node("executor", executor_node)
    builder.add_node("quality_response", quality_response_node)

    # 定义路由函数
    def should_review(state: PlanAgentState) -> str:
        """决定是否需要人工审核"""
        current_plan = state.get("current_plan")
        if not current_plan:
            return "quality_response"
        return "human_review" if current_plan.needs_review else "executor"

    def should_replan(state: PlanAgentState) -> str:
        """决定是否需要重新规划"""
        quality_assessment = state.get("quality_assessment")
        replanning_count = state.get("replanning_count", 0)

        if not quality_assessment:
            return "__end__"

        # 检查是否需要重规划且未超过最大次数
        max_iterations = 3  # 可配置
        if (quality_assessment.execution_quality < 0.6 and
            replanning_count < max_iterations):
            return "planner"
        return "__end__"

    # 设置图结构
    builder.add_edge(START, "planner")
    builder.add_conditional_edges("planner", should_review)
    builder.add_edge("human_review", "executor")
    builder.add_edge("executor", "quality_response")
    builder.add_conditional_edges("quality_response", should_replan)

    # 编译图
    return builder.compile(
        checkpointer=checkpointer,
        interrupt_before=["human_review"]
    )
