"""
Plan Agent 构建器
"""
from langgraph.graph import StateGraph, START

from .types import PlanAgentState
from .nodes.query_rewrite import create_query_rewrite_node
from .nodes.planning import create_planner_node
from .nodes.human_feedback import create_human_review_node
from .nodes.execution import create_executor_node
from .nodes.response_generator import create_quality_response_node


async def create_plan_agent_graph(llm, mcp_client, checkpointer=None, available_tools=None):
    """创建完整的5节点Plan Agent图"""

    # 获取并绑定工具到LLM
    if available_tools is None:
        available_tools = await mcp_client.get_tools()

    # 绑定工具到LLM
    tool_bound_llm = llm.bind_tools(available_tools, tool_choice="none")

    builder = StateGraph(PlanAgentState)

    # 创建节点实例
    query_rewrite_node = create_query_rewrite_node(llm)
    planner_node = create_planner_node(llm, mcp_client, available_tools)
    human_review_node = create_human_review_node()
    executor_node = create_executor_node(mcp_client, available_tools)
    quality_response_node = create_quality_response_node(tool_bound_llm)

    # 添加节点
    builder.add_node("query_rewrite", query_rewrite_node)
    builder.add_node("planner", planner_node)
    builder.add_node("human_review", human_review_node)
    builder.add_node("executor", executor_node)
    builder.add_node("quality_response", quality_response_node)

    # 设置图结构 - 5节点工作流
    # 使用Command对象处理路由，不需要conditional_edges
    builder.add_edge(START, "query_rewrite")
    # 其他路由由各节点的Command对象处理

    # 编译图
    return builder.compile(
        checkpointer=checkpointer,
        interrupt_before=["human_review"]
    )
