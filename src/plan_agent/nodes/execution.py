"""
工具执行节点
"""
import asyncio
import time
from typing import Dict, Any

from ..types import PlanAgentState, ToolResult
from src.utils import logger
from src.utils.node_debugger import debug_node


def create_executor_node(mcp_client, available_tools=None):
    @debug_node("执行节点")
    async def executor_node(state: PlanAgentState) -> Dict[str, Any]:
        """优化的执行节点 - 返回状态字典"""
        current_plan = state.get("current_plan")

        if not current_plan or not current_plan.steps:
            logger.warning("没有可执行的计划步骤")
            return {
                "errors": state.get("errors", []) + ["没有可执行的计划步骤"],
                "tool_results": []
            }

        logger.info(f"🚀 开始执行计划: {current_plan.title}")
        logger.info(f"📊 计划步骤数: {len(current_plan.steps)}")

        # 获取工具
        if available_tools is not None:
            tools = available_tools
        else:
            tools = await mcp_client.get_tools()

        try:
            # 根据步骤数量选择执行策略
            if len(current_plan.steps) > 1:
                logger.info("🔄 使用并行执行策略")
                tool_results = await execute_tools_parallel(current_plan.steps, tools)
            else:
                logger.info("➡️ 使用顺序执行策略")
                tool_results = await execute_tools_sequential(current_plan.steps, tools)

            # 统计执行结果
            success_count = sum(1 for r in tool_results if r.success)
            logger.info(f"✅ 执行完成: {success_count}/{len(tool_results)} 成功")

            return {
                "tool_results": tool_results
            }

        except Exception as e:
            logger.error(f"执行过程发生异常: {e}")
            return {
                "errors": state.get("errors", []) + [f"执行异常: {str(e)}"],
                "tool_results": []
            }

    return executor_node


async def execute_single_tool_with_retry(tool, tool_name, params, max_retries=3):
    """执行单个工具，带重试机制"""
    for attempt in range(max_retries):
        try:
            start_time = time.time()
            
            # 记录调用日志
            logger.info(f"🔧 [{tool_name}] 开始执行")
            logger.info(f"📥 [{tool_name}] 入参: {params}")
            
            result = await tool.ainvoke(params)
            duration = time.time() - start_time
            
            # 记录输出日志
            result_preview = str(result)[:300] + "..." if len(str(result)) > 300 else str(result)
            logger.info(f"📤 [{tool_name}] 出参: {result_preview}")
            logger.info(f"✅ [{tool_name}] 执行成功，耗时 {duration:.2f}s")
            
            # 检查是否为服务器错误需要重试
            if is_server_error(result) and attempt < max_retries - 1:
                logger.warning(f"⚠️ [{tool_name}] 服务器错误，第{attempt + 1}次重试")
                await asyncio.sleep(1)  # 等待1秒后重试
                continue
            
            return ToolResult(
                tool_name=tool_name,
                success=True,
                result=result,
                duration=duration
            )
            
        except Exception as e:
            logger.error(f"❌ [{tool_name}] 第{attempt + 1}次执行失败: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # 等待1秒后重试
                continue
            else:
                return ToolResult(
                    tool_name=tool_name,
                    success=False,
                    error=str(e)
                )


def is_server_error(result):
    """判断是否为服务器错误"""
    if isinstance(result, str):
        return "服务器开小差" in result or "status_code\":20010" in result
    elif isinstance(result, dict):
        return result.get("status_code") == 20010
    return False


async def execute_tools_sequential(steps, tools):
    """顺序执行工具"""
    results = []
    tool_map = {tool.name: tool for tool in tools}
    
    for step in steps:
        tool_name = step.tool
        if tool_name not in tool_map:
            logger.warning(f"工具 {tool_name} 不存在")
            continue
            
        tool = tool_map[tool_name]
        result = await execute_single_tool_with_retry(tool, tool_name, step.params)
        results.append(result)
    
    return results


async def execute_tools_parallel(steps, tools):
    """并行执行工具"""
    tool_map = {tool.name: tool for tool in tools}
    
    async def execute_step(step):
        tool_name = step.tool
        if tool_name not in tool_map:
            logger.warning(f"工具 {tool_name} 不存在")
            return None
            
        tool = tool_map[tool_name]
        return await execute_single_tool_with_retry(tool, tool_name, step.params)
    
    tasks = [execute_step(step) for step in steps]
    results = await asyncio.gather(*tasks, return_exceptions=False)
    
    return [r for r in results if r is not None]
