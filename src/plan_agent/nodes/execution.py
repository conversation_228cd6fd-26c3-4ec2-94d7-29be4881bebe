"""
工具执行节点
"""
import asyncio
import time
from langgraph.types import Command

from ..types import PlanAgentState, ToolResult
from src.utils import logger
from src.utils.node_debugger import debug_node


def create_executor_node(mcp_client, available_tools=None):
    @debug_node("执行节点")
    async def executor_node(state: PlanAgentState) -> Command:
        """执行节点 - 返回Command对象"""
        current_plan = state.get("current_plan")

        if not current_plan or not current_plan.steps:
            logger.warning("没有可执行的计划步骤")
            return Command(
                update={
                    "errors": state.get("errors", []) + ["没有可执行的计划步骤"],
                    "tool_results": []
                },
                goto="quality_response"
            )

        logger.info(f"🚀 开始执行计划: {current_plan.title}")
        logger.info(f"📊 计划步骤数: {len(current_plan.steps)}")

        # 获取工具
        if available_tools is not None:
            tools = available_tools
        else:
            tools = await mcp_client.get_tools()

        try:
            # 检查是否有依赖关系，如果有则必须使用顺序执行
            has_dependencies = any(
                any("step" in str(param_value) for param_value in step.params.values())
                for step in current_plan.steps
            )

            if has_dependencies or len(current_plan.steps) == 1:
                logger.info("➡️ 使用顺序执行策略（有依赖关系或单步骤）")
                tool_results = await execute_tools_sequential_with_state(current_plan.steps, tools, state)
            else:
                logger.info("🔄 使用并行执行策略")
                tool_results = await execute_tools_parallel(current_plan.steps, tools)

            # 统计执行结果
            success_count = sum(1 for r in tool_results if r.success)
            logger.info(f"✅ 执行完成: {success_count}/{len(tool_results)} 成功")

            return Command(
                update={
                    "tool_results": tool_results
                },
                goto="quality_response"
            )

        except Exception as e:
            logger.error(f"执行过程发生异常: {e}")
            return Command(
                update={
                    "errors": state.get("errors", []) + [f"执行异常: {str(e)}"],
                    "tool_results": []
                },
                goto="quality_response"
            )

    return executor_node


async def execute_single_tool_with_retry(tool, tool_name, params, max_retries=3):
    """执行单个工具，带重试机制"""
    for attempt in range(max_retries):
        try:
            start_time = time.time()
            
            # 记录调用日志
            logger.info(f"🔧 [{tool_name}] 开始执行")
            logger.info(f"📥 [{tool_name}] 入参: {params}")
            
            result = await tool.ainvoke(params)
            duration = time.time() - start_time
            
            # 记录输出日志
            result_preview = str(result)[:300] + "..." if len(str(result)) > 300 else str(result)
            logger.info(f"📤 [{tool_name}] 出参: {result_preview}")
            logger.info(f"✅ [{tool_name}] 执行成功，耗时 {duration:.2f}s")
            
            # 检查是否为服务器错误需要重试
            if is_server_error(result) and attempt < max_retries - 1:
                logger.warning(f"⚠️ [{tool_name}] 服务器错误，第{attempt + 1}次重试")
                await asyncio.sleep(1)  # 等待1秒后重试
                continue
            
            return ToolResult(
                tool_name=tool_name,
                success=True,
                result=result,
                duration=duration
            )
            
        except Exception as e:
            logger.error(f"❌ [{tool_name}] 第{attempt + 1}次执行失败: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # 等待1秒后重试
                continue
            else:
                return ToolResult(
                    tool_name=tool_name,
                    success=False,
                    error=str(e)
                )


def is_server_error(result):
    """判断是否为服务器错误"""
    if isinstance(result, str):
        return "服务器开小差" in result or "status_code\":20010" in result
    elif isinstance(result, dict):
        return result.get("status_code") == 20010
    return False


async def execute_tools_sequential(steps, tools):
    """顺序执行工具，支持步骤间依赖"""
    results = []
    tool_map = {tool.name: tool for tool in tools}
    step_results = {}  # 存储每个步骤的结果

    for i, step in enumerate(steps, 1):
        tool_name = step.tool
        if tool_name not in tool_map:
            logger.warning(f"工具 {tool_name} 不存在")
            continue

        # 解析参数依赖
        resolved_params = resolve_step_dependencies(step.params, step_results)

        tool = tool_map[tool_name]
        result = await execute_single_tool_with_retry(tool, tool_name, resolved_params)
        results.append(result)

        # 保存步骤结果供后续步骤使用
        if result and result.success:
            step_results[f"step{i}"] = extract_result_value(result.result)

    return results


async def execute_tools_parallel(steps, tools):
    """并行执行工具"""
    tool_map = {tool.name: tool for tool in tools}
    
    async def execute_step(step):
        tool_name = step.tool
        if tool_name not in tool_map:
            logger.warning(f"工具 {tool_name} 不存在")
            return None
            
        tool = tool_map[tool_name]
        return await execute_single_tool_with_retry(tool, tool_name, step.params)
    
    tasks = [execute_step(step) for step in steps]
    results = await asyncio.gather(*tasks, return_exceptions=False)
    
    return [r for r in results if r is not None]


async def execute_tools_sequential_with_state(steps, tools, state):
    """基于LangGraph状态管理的顺序执行（推荐方案）"""
    results = []
    tool_map = {tool.name: tool for tool in tools}

    # 使用状态来存储中间结果
    execution_results = state.get("execution_results", {})

    for i, step in enumerate(steps, 1):
        tool_name = step.tool
        if tool_name not in tool_map:
            logger.warning(f"工具 {tool_name} 不存在")
            continue

        # 解析参数依赖（基于状态）
        resolved_params = resolve_step_dependencies_from_state(step.params, execution_results)

        tool = tool_map[tool_name]
        result = await execute_single_tool_with_retry(tool, tool_name, resolved_params)
        results.append(result)

        # 将结果保存到状态中
        if result and result.success:
            step_key = f"step{i}"
            extracted_value = extract_result_value(result.result)
            execution_results[step_key] = extracted_value
            logger.info(f"💾 保存步骤结果: {step_key} = {extracted_value}")

    return results


def resolve_step_dependencies_from_state(params, execution_results):
    """基于状态解析步骤依赖（LangGraph推荐方式）"""
    import re

    resolved_params = {}

    for key, value in params.items():
        if isinstance(value, str) and "step" in value:
            # 匹配 step1.result, step2.result 等模式
            step_pattern = r'step(\d+)\.result'
            match = re.search(step_pattern, value)

            if match:
                step_num = match.group(1)
                step_key = f"step{step_num}"

                if step_key in execution_results:
                    resolved_params[key] = execution_results[step_key]
                    logger.info(f"🔗 解析依赖: {key} = {step_key} -> {resolved_params[key]}")
                else:
                    logger.warning(f"⚠️ 依赖步骤 {step_key} 结果不存在，使用原值")
                    resolved_params[key] = value
            else:
                resolved_params[key] = value
        else:
            resolved_params[key] = value

    return resolved_params


def resolve_step_dependencies(params, step_results):
    """解析步骤参数中的依赖关系"""
    import re

    resolved_params = {}

    for key, value in params.items():
        if isinstance(value, str) and "step" in value:
            # 匹配 step1.result, step2.result 等模式
            step_pattern = r'step(\d+)\.result'
            match = re.search(step_pattern, value)

            if match:
                step_num = match.group(1)
                step_key = f"step{step_num}"

                if step_key in step_results:
                    resolved_params[key] = step_results[step_key]
                    logger.info(f"🔗 解析依赖: {key} = {step_key} -> {resolved_params[key]}")
                else:
                    logger.warning(f"⚠️ 依赖步骤 {step_key} 结果不存在，使用原值")
                    resolved_params[key] = value
            else:
                resolved_params[key] = value
        else:
            resolved_params[key] = value

    return resolved_params


def extract_result_value(result):
    """从工具执行结果中提取有用的值"""
    import json

    if isinstance(result, str):
        try:
            # 尝试解析JSON
            data = json.loads(result)
            if isinstance(data, dict):
                # 优先提取常见的ID字段
                for id_field in ['faction_id', 'id', 'anchor_id']:
                    if id_field in data:
                        return data[id_field]

                # 如果有data字段，尝试从中提取
                if 'data' in data and isinstance(data['data'], dict):
                    data_obj = data['data']

                    # 查找faction_data中的第一个faction_id
                    if 'faction_data' in data_obj and isinstance(data_obj['faction_data'], list):
                        if len(data_obj['faction_data']) > 0:
                            first_faction = data_obj['faction_data'][0]
                            if isinstance(first_faction, dict) and 'faction_info' in first_faction:
                                faction_info = first_faction['faction_info']
                                if 'faction_id' in faction_info:
                                    return faction_info['faction_id']

                # 返回整个data对象
                if 'data' in data:
                    return data['data']

            return data
        except json.JSONDecodeError:
            return result

    return result
