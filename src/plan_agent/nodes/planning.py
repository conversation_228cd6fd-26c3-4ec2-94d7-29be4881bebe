"""
智能规划节点：查询分析 + 工具选择 + 计划生成
"""
from typing import List, Dict, Any
from langchain_core.messages import SystemMessage, HumanMessage

from pydantic import BaseModel, Field

from ..types import PlanAgentState, ExecutionPlan, PlanStep
from src.utils import logger
from src.utils.node_debugger import debug_node
from src.prompts.planning_prompts import (
    INITIAL_PLANNING_TEMPLATE, 
    REPLANNING_TEMPLATE,
    PREVIOUS_RESULTS_TEMPLATE,
    QUALITY_FEEDBACK_TEMPLATE,
    get_planning_system_message
)


class PlanningOutput(BaseModel):
    """规划输出结构化模型 - 用于LLM structured output"""
    query_intent: str = Field(description="查询意图分析")
    selected_tools: List[str] = Field(description="选中工具名称")
    plan_title: str = Field(description="计划标题")
    reasoning: str = Field(description="规划思路")
    steps: List[Dict[str, Any]] = Field(description="执行步骤")
    needs_review: bool = Field(description="是否需要审核")


def create_execution_plan_from_output(planning_result: PlanningOutput, tools) -> ExecutionPlan:
    """从规划输出创建执行计划"""
    # 转换步骤格式
    plan_steps = []
    for i, step_dict in enumerate(planning_result.steps):
        plan_step = PlanStep(
            step_id=step_dict.get("step_id", f"step_{i+1}"),
            tool=step_dict.get("tool", ""),
            params=step_dict.get("params", {}),
            description=step_dict.get("description", ""),
            dependencies=step_dict.get("dependencies", [])
        )
        plan_steps.append(plan_step)
    
    return ExecutionPlan(
        title=planning_result.plan_title,
        reasoning=planning_result.reasoning,
        steps=plan_steps,
        needs_review=planning_result.needs_review
    )


def determine_next_node(execution_plan: ExecutionPlan) -> str:
    """确定下一个节点"""
    if execution_plan.needs_review:
        return "human_review"
    else:
        return "executor"


def create_planner_node(llm, mcp_client, available_tools=None):
    @debug_node("规划节点")
    async def planner_node(state: PlanAgentState) -> Dict[str, Any]:
        """优化的规划节点 - 返回状态字典而非Command"""
        user_query = state.get("user_query", "")
        is_replanning = state.get("replanning_count", 0) > 0

        if available_tools is not None:
            tools = available_tools
        else:
            tools = await mcp_client.get_tools()

        logger.info(f"📋 规划任务: {'重新规划' if is_replanning else '初始规划'}")
        logger.info(f"📋 可用工具: {len(tools)}个")

        # 构建提示内容
        if is_replanning:
            previous_results = state.get("tool_results", [])
            quality_info = state.get("quality_assessment")
            results_summary = PREVIOUS_RESULTS_TEMPLATE.render(results=previous_results)
            quality_feedback = QUALITY_FEEDBACK_TEMPLATE.render(quality_info=quality_info)

            prompt_content = REPLANNING_TEMPLATE.render(
                user_query=user_query,
                available_tools=tools,
                results_summary=results_summary,
                quality_feedback=quality_feedback
            )
        else:
            prompt_content = INITIAL_PLANNING_TEMPLATE.render(
                user_query=user_query,
                available_tools=tools
            )

        # 构建消息
        messages = [
            SystemMessage(content=get_planning_system_message(is_replanning)),
            HumanMessage(content=prompt_content)
        ]

        try:
            # 使用structured output
            structured_llm = llm.with_structured_output(PlanningOutput)
            planning_result = await structured_llm.ainvoke(messages)

            execution_plan = create_execution_plan_from_output(planning_result, tools)

            return {
                "current_plan": execution_plan,
                "messages": state.get("messages", []) + messages
            }

        except Exception as e:
            logger.error(f"规划失败: {e}")
            return {
                "errors": state.get("errors", []) + [f"规划失败: {str(e)}"]
            }

    return planner_node
