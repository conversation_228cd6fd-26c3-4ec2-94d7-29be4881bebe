from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.types import Command

from ..types import PlanAgentState, QueryRewriteOutput
from src.prompts.plan_system_prompt import QUERY_REWRITE_TEMPLATE
from src.prompts.query_rewrite_prompts import QUERY_REWRITE_SYSTEM_MESSAGE
from src.utils.node_debugger import debug_node


def get_query_rewrite_system_message() -> str:
    return QUERY_REWRITE_SYSTEM_MESSAGE


def format_conversation_history(messages) -> str:
    """格式化对话历史"""
    formatted = []
    for msg in messages[-6:]:  # 只取最近6条消息
        if isinstance(msg, HumanMessage):
            formatted.append(f"用户: {msg.content}")
        elif isinstance(msg, AIMessage):
            formatted.append(f"助手: {msg.content[:200]}...")  # 截取前200字符
    return "\n".join(formatted)


def create_query_rewrite_node(llm):
    @debug_node("查询改写节点")
    async def query_rewrite_node(state: PlanAgentState) -> Command:
        user_query = state.get("user_query", "")
        messages = state.get("messages", [])

        if not user_query.strip():
            return Command(
                update={"errors": ["用户查询为空"]},
                goto="quality_response"
            )

        structured_llm = llm.with_structured_output(QueryRewriteOutput)

        # 构建包含历史上下文的提示
        context_info = ""
        if len(messages) > 1:  # 有历史消息
            context_info = f"\n\n历史对话上下文:\n{format_conversation_history(messages[:-1])}"

        prompt_content = QUERY_REWRITE_TEMPLATE.render(
            original_question=user_query,
            conversation_context=context_info
        )

        query_rewrite_result = await structured_llm.ainvoke([
            SystemMessage(content=get_query_rewrite_system_message()),
            HumanMessage(content=prompt_content)
        ])
        
        return Command(
            update={
                "optimized_query": query_rewrite_result.rewritten_query,
                "query_intent": query_rewrite_result.intent_analysis
            },
            goto="planner"
        )
    
    return query_rewrite_node


