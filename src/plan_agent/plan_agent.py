"""
PlanAgent主类
"""
import time
from typing import Dict, Any, List
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.types import Command

from .builder import create_plan_agent_graph
from .types import PlanAgentState, ProcessingMode
from src.utils import logger


class PlanAgent:
    """5节点Plan Agent: Query Rewrite → Plan → Human Review → Execute → Quality Response"""
    
    def __init__(self, llm, mcp_client, memory, checkpointer):
        self.llm = llm
        self.mcp_client = mcp_client
        self.memory = memory
        self.checkpointer = checkpointer
        
        self.tools = []
        self.graph = None
        
        logger.info("PlanAgent初始化完成")
    
    async def invoke(self, user_input: str, session_id: str) -> Dict[str, Any]:
        await self.ensure_tools_loaded()

        # 创建包含工具的图
        if self.graph is None:
            self.graph = await create_plan_agent_graph(
                llm=self.llm,
                mcp_client=self.mcp_client,
                checkpointer=self.checkpointer,
                available_tools=self.tools
            )

        config = RunnableConfig()
        config.setdefault("configurable", {})["thread_id"] = session_id

        # 获取或创建会话状态
        state = await self.get_or_create_session_state(user_input, session_id, config)
        
        start_time = time.time()
        
        try:
            logger.info(f"处理请求: {user_input}")
            
            execution_result = await self.graph.ainvoke(state, config=config)
            
            processing_duration = time.time() - start_time
            quality_assessment = execution_result.get("quality_assessment")
            tool_count = len(execution_result.get("tool_results", []))
            
            final_response = quality_assessment.final_response if quality_assessment else "处理失败"
            is_successful = bool(quality_assessment and quality_assessment.execution_quality > 0.5)
            
            logger.info(f"处理完成: {processing_duration:.2f}s, {tool_count}个工具")
            
            self.save_conversation_to_memory(user_input, final_response, session_id)
            
            return {
                "success": is_successful,
                "response": final_response,
                "session_id": session_id,
                "processing_mode": execution_result.get("processing_mode", ProcessingMode.STRUCTURED).name,
                "tool_count": tool_count,
                "duration": processing_duration,
                "quality_score": quality_assessment.execution_quality if quality_assessment else 0.0
            }
            
        except Exception as e:
            # 检查是否是interrupt异常
            if hasattr(e, '__cause__') and str(e.__cause__).startswith('Interrupt'):
                processing_duration = time.time() - start_time
                interrupt_msg = str(e.__cause__) if e.__cause__ else str(e)
                logger.info(f"需要人工输入: {interrupt_msg}")
                
                return {
                    "success": False,
                    "interrupted": True,
                    "interrupt_message": interrupt_msg,
                    "session_id": session_id,
                    "duration": processing_duration,
                    "config": config  # 返回config以便后续恢复
                }
            else:
                processing_duration = time.time() - start_time
                logger.error(f"处理失败: {e}")
                
                return {
                    "success": False,
                    "response": f"处理过程中出现错误: {e}",
                    "session_id": session_id,
                    "error": str(e),
                    "duration": processing_duration
                }
    
    async def resume(self, user_response: str, config: RunnableConfig) -> Dict[str, Any]:
        """恢复被中断的执行"""
        start_time = time.time()
        
        try:
            logger.info(f"恢复执行，用户输入: {user_response}")
            
            # 使用Command.resume恢复执行
            from langgraph.types import Command
            if self.graph is None:
                raise ValueError("图未初始化，请先调用invoke方法")
                
            execution_result = await self.graph.ainvoke(
                Command(resume=user_response), 
                config=config
            )
            
            processing_duration = time.time() - start_time
            quality_assessment = execution_result.get("quality_assessment")
            tool_count = len(execution_result.get("tool_results", []))
            
            final_response = quality_assessment.final_response if quality_assessment else "处理失败"
            is_successful = bool(quality_assessment and quality_assessment.execution_quality > 0.5)
            
            logger.info(f"恢复完成: {processing_duration:.2f}s, {tool_count}个工具")
            
            return {
                "success": is_successful,
                "response": final_response,
                "session_id": config.get("configurable", {}).get("thread_id"),
                "tool_count": tool_count,
                "duration": processing_duration,
                "quality_score": quality_assessment.execution_quality if quality_assessment else 0.0
            }
            
        except Exception as e:
            # 检查是否再次需要interrupt
            if hasattr(e, '__cause__') and str(e.__cause__).startswith('Interrupt'):
                processing_duration = time.time() - start_time
                interrupt_msg = str(e.__cause__) if e.__cause__ else str(e)
                logger.info(f"再次需要人工输入: {interrupt_msg}")
                
                return {
                    "success": False,
                    "interrupted": True,
                    "interrupt_message": interrupt_msg,
                    "session_id": config.get("configurable", {}).get("thread_id"),
                    "duration": processing_duration,
                    "config": config
                }
            else:
                processing_duration = time.time() - start_time
                logger.error(f"恢复失败: {e}")
                
                return {
                    "success": False,
                    "response": f"恢复过程中出现错误: {e}",
                    "session_id": config.get("configurable", {}).get("thread_id"),
                    "error": str(e),
                    "duration": processing_duration
                }
    
    async def ensure_tools_loaded(self):
        """确保工具已加载"""
        if not self.tools and self.mcp_client:
            try:
                self.tools = await self.mcp_client.get_tools()
                logger.info(f"加载工具: {len(self.tools)}个")
            except Exception as e:
                logger.error(f"工具加载失败: {e}")
                self.tools = []
    
    def save_conversation_to_memory(self, user_input: str, response: str, session_id: str):
        """保存对话到记忆"""
        if self.memory and response:
            try:
                self.memory.add_conversation(
                    user_input=user_input,
                    assistant_response=response,
                    user_id=session_id
                )
            except Exception as e:
                logger.warning(f"保存记忆失败: {e}")
    
    async def astream(self, user_input: str, **kwargs):
        """流式处理接口"""
        result = await self.invoke(user_input, **kwargs)
        yield result

    async def get_or_create_session_state(self, user_input: str, session_id: str, config: RunnableConfig) -> PlanAgentState:
        """获取或创建会话状态，支持多轮对话"""
        try:
            # 尝试从checkpointer获取现有状态
            if self.checkpointer and self.graph:
                # 获取最新的checkpoint
                checkpoint = await self.checkpointer.aget(config)
                if checkpoint and hasattr(checkpoint, 'channel_values') and checkpoint.channel_values:
                    # 从checkpoint恢复状态
                    existing_state = checkpoint.channel_values
                    logger.info(f"📚 恢复会话状态: {len(existing_state.get('messages', []))}条历史消息")

                    # 添加新的用户消息到现有状态
                    messages = existing_state.get("messages", [])
                    messages.append(HumanMessage(content=user_input))

                    # 更新状态
                    updated_state = PlanAgentState(
                        messages=messages,
                        user_query=user_input,
                        session_id=session_id,
                        replanning_count=0,  # 新轮次重置
                        tool_results=[],     # 新轮次重置
                        errors=[],           # 新轮次重置
                        # 保留其他可能的状态字段
                        current_plan=existing_state.get("current_plan"),
                        quality_assessment=existing_state.get("quality_assessment")
                    )

                    return updated_state
        except Exception as e:
            logger.warning(f"恢复会话状态失败: {e}")

        # 创建新的会话状态
        logger.info("🆕 创建新会话状态")
        return PlanAgentState(
            messages=[HumanMessage(content=user_input)],
            user_query=user_input,
            session_id=session_id,
            replanning_count=0,
            tool_results=[],
            errors=[]
        )

    async def get_conversation_history(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取会话历史"""
        try:
            config = RunnableConfig()
            config.setdefault("configurable", {})["thread_id"] = session_id

            if self.checkpointer:
                checkpoint = await self.checkpointer.aget(config)
                if checkpoint and hasattr(checkpoint, 'channel_values') and checkpoint.channel_values:
                    messages = checkpoint.channel_values.get("messages", [])

                    # 转换为简单格式
                    history = []
                    for msg in messages[-limit*2:]:  # 获取最近的消息对
                        if isinstance(msg, HumanMessage):
                            history.append({
                                "role": "user",
                                "content": msg.content,
                                "timestamp": getattr(msg, 'timestamp', None)
                            })
                        elif isinstance(msg, AIMessage):
                            history.append({
                                "role": "assistant",
                                "content": msg.content,
                                "timestamp": getattr(msg, 'timestamp', None)
                            })

                    return history[-limit:]  # 返回最近的limit条记录
        except Exception as e:
            logger.warning(f"获取会话历史失败: {e}")

        return []

    async def clear_session(self, session_id: str) -> bool:
        """清除会话状态"""
        try:
            config = RunnableConfig()
            config.setdefault("configurable", {})["thread_id"] = session_id

            if self.checkpointer:
                # 清除checkpointer中的状态
                # 对于MemorySaver，我们可以尝试删除对应的存储
                if hasattr(self.checkpointer, 'storage'):
                    # 清除内存中的数据
                    thread_id = config.get("configurable", {}).get("thread_id")
                    if thread_id and hasattr(self.checkpointer.storage, 'pop'):
                        self.checkpointer.storage.pop(thread_id, None)
                logger.info(f"🗑️ 已清除会话: {session_id}")
                return True
        except Exception as e:
            logger.error(f"清除会话失败: {e}")

        return False



