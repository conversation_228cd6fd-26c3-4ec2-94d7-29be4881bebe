from typing import TypedDict, List, Dict, Any, Optional
from enum import Enum
from langchain_core.messages import BaseMessage
from pydantic import BaseModel, Field

ProcessingMode = Enum("ProcessingMode", ["STRUCTURED", "CONVERSATIONAL"])


class QueryRewriteOutput(BaseModel):
    """查询改写输出"""
    rewritten_query: str = Field(description="改写后的查询")
    intent_analysis: str = Field(description="查询意图分析")


class TaskStep(BaseModel):
    """任务步骤"""
    title: str = Field(description="步骤标题")
    tool: str = Field(description="工具名称")
    params: Dict[str, Any] = Field(default_factory=dict, description="工具参数")
    description: str = Field(default="", description="步骤描述")


class PlanStep(BaseModel):
    """计划步骤"""
    step_id: str = Field(description="步骤唯一标识")
    tool: str = Field(description="使用的工具名称")
    params: dict = Field(description="工具参数")
    description: str = Field(description="步骤描述")
    dependencies: List[str] = Field(default=[], description="依赖的步骤ID列表")


class ExecutionPlan(BaseModel):
    """执行计划"""
    title: str = Field(description="计划标题")
    reasoning: str = Field(description="规划思路")
    steps: List[PlanStep] = Field(default_factory=list, description="执行步骤")
    needs_review: bool = Field(default=False, description="是否需要审核")


class PlanningOutput(BaseModel):
    """规划输出"""
    query_intent: str = Field(description="查询意图分析")
    selected_tools: List[str] = Field(description="选择的工具列表")
    plan_title: str = Field(description="计划标题")
    reasoning: str = Field(description="规划推理过程")
    steps: List[PlanStep] = Field(description="执行步骤")
    needs_review: bool = Field(description="是否需要人工审核")


class ToolResult(BaseModel):
    """工具结果"""
    tool_name: str = Field(description="工具名称")
    success: bool = Field(description="是否成功")
    result: Optional[str] = Field(default=None, description="执行结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    duration: Optional[float] = Field(default=None, description="执行耗时(秒)")


class QualityAssessment(BaseModel):
    execution_quality: int = Field(description="执行质量评分(1-10)")
    response_quality: int = Field(description="响应质量评分(1-10)")
    task_completeness: int = Field(description="任务完成度评分(1-10)")
    overall_score: int = Field(description="总体评分(1-10)")
    improvement_suggestions: List[str] = Field(description="改进建议")
    final_response: str = Field(description="最终用户回答")


class QualityResponseOutput(BaseModel):
    """质量响应输出"""
    execution_quality: float = Field(description="执行质量评分 0-1")
    response_quality: float = Field(description="响应质量评分 0-1") 
    task_completeness: float = Field(description="任务完整度 0-1")
    needs_replanning: bool = Field(description="是否需要重规划")
    replanning_reason: str = Field(default="", description="重规划原因")
    final_response: str = Field(description="最终用户回答")


class PlanAgentState(TypedDict, total=False):
    """优化的PlanAgent状态 - 减少字段，明确职责"""

    # 核心状态
    messages: List[BaseMessage]
    user_query: str
    session_id: str

    # 规划相关
    current_plan: Optional[ExecutionPlan]
    replanning_count: int

    # 执行相关
    tool_results: List[ToolResult]
    execution_results: Dict[str, Any]  # 存储步骤间的中间结果

    # 评估相关
    quality_assessment: Optional[QualityAssessment]

    # 错误处理
    errors: List[str]


# ... existing code ... 
